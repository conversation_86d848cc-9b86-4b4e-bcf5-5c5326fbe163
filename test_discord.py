#!/usr/bin/env python3
import time
import yaml
import discord_handler

def test_discord_connection():
    """Test Discord connection"""
    print("🔄 Đang test kết nối Discord...")
    
    # Load config
    with open("config.yaml", "r") as file:
        config = yaml.safe_load(file)
    
    # Khởi tạo Discord handler
    discord = discord_handler.DiscordHandler(
        token=config["DISCORD_TOKEN"],
        admin_id=config["DISCORD_ADMIN_ID"],
        guild_id=config["DISCORD_GUILD_ID"],
        channel_name=config["DISCORD_CHANNEL"]
    )
    
    # Start bot
    print("🚀 Khởi động Discord bot...")
    discord.start_bot()
    
    # Chờ bot sẵn sàng
    print("⏳ Chờ bot sẵn sàng...")
    for i in range(30):  # Chờ tối đa 30 giây
        if discord.bot.is_ready():
            print("✅ Bot đã sẵn sàng!")
            break
        time.sleep(1)
        print(f"   Đang chờ... {i+1}/30")
    else:
        print("❌ Bot không thể sẵn sàng trong 30 giây")
        return False
    
    # Test gửi tin nhắn
    print("📤 Test gửi tin nhắn...")
    success = discord.send_message_sync("🧪 **TEST MESSAGE**\nHệ thống Discord đã hoạt động thành công!")
    
    if success:
        print("✅ Gửi tin nhắn thành công!")
    else:
        print("❌ Gửi tin nhắn thất bại!")
    
    # Dừng bot
    print("🛑 Dừng bot...")
    discord.stop_bot_updates()
    
    return success

if __name__ == "__main__":
    try:
        result = test_discord_connection()
        if result:
            print("\n🎉 Test Discord thành công! Hệ thống đã sẵn sàng chuyển từ Telegram sang Discord.")
        else:
            print("\n❌ Test Discord thất bại! Cần kiểm tra lại cấu hình.")
    except Exception as e:
        print(f"\n💥 Lỗi trong quá trình test: {e}")
