import json
import time
import numpy as np
import pandas as pd
import os
from logging.handlers import RotatingFileHandler
from typing import Dict, <PERSON><PERSON>, Optional
from datetime import datetime, timedelta, timezone
import random
import discord_handler

try:
    from talib import RSI, ATR
except ImportError:
    print("TA-Lib not found. Please install it: https://mrjbq7.github.io/ta-lib/install.html")
    exit(1)

import pandas_ta as ta
from contract_v1_python_demo import get_kline
import yaml
import symbol
import trading_strategy as strategy
import logging
from basic_call import place_order  

LOG_DIR = "logs"
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

def setup_logging():
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    formatter = logging.Formatter(log_format)
    trade_logger = logging.getLogger('trade')
    trade_logger.setLevel(logging.INFO)
    trade_handler = RotatingFileHandler(os.path.join(LOG_DIR, 'trade.log'), maxBytes=10*1024*1024, backupCount=5)
    trade_handler.setFormatter(formatter)
    trade_logger.addHandler(trade_handler)

    logic_logger = logging.getLogger('logic')
    logic_logger.setLevel(logging.DEBUG)
    logic_handler = RotatingFileHandler(os.path.join(LOG_DIR, 'logic.log'), maxBytes=10*1024*1024, backupCount=5)
    logic_handler.setFormatter(formatter)
    logic_logger.addHandler(logic_handler)

    error_logger = logging.getLogger('error')
    error_logger.setLevel(logging.ERROR)
    error_handler = RotatingFileHandler(os.path.join(LOG_DIR, 'error.log'), maxBytes=10*1024*1024, backupCount=5)
    error_handler.setFormatter(formatter)
    error_logger.addHandler(error_handler)

    return trade_logger, logic_logger, error_logger

trade_logger, logic_logger, error_logger = setup_logging()

# --- Load cấu hình từ config.yaml ---
with open("config.yaml", "r") as file:
    config = yaml.safe_load(file)
DISCORD_TOKEN = config["DISCORD_TOKEN"]
DISCORD_ADMIN_ID = config["DISCORD_ADMIN_ID"]
DISCORD_GUILD_ID = config["DISCORD_GUILD_ID"]
DISCORD_CHANNEL = config["DISCORD_CHANNEL"]
BASE_USDT = float(config["BASE_USDT"])
BASE_LEVERAGE = int(config["BASE_LEVERAGE"])
BASE_BALANCE = float(config["BASE_BALANCE"])
RR_RATIO = float(config["RR_RATIO"])
SLEEP_INTERVAL = int(config["SLEEP_INTERVAL"])
WATCHLIST_RETENTION_MINUTES = int(config["WATCHLIST_RETENTION_MINUTES"])
ATR_TIMEFRAME = config["ATR_TIMEFRAME"]
ATR_PERIOD = int(config["ATR_PERIOD"])
ATR_SL_MULTIPLIER = float(config["ATR_SL_MULTIPLIER"])
MIN_SL_PERCENTAGE = float(config["MIN_SL_PERCENTAGE"])
ORDER_WARN_DURATION_H1 = int(config["ORDER_WARN_DURATION_H1"])
ORDER_WARN_DURATION_H2 = int(config["ORDER_WARN_DURATION_H2"])
ORDER_SUMMARY_INTERVAL_MIN = int(config["ORDER_SUMMARY_INTERVAL_MIN"])
RSI_THRESHOLD = float(config.get("RSI_THRESHOLD", 21.0))
M1_VOLUME_AVG_PERIOD = int(config.get("M1_VOLUME_AVG_PERIOD", 10))
ENTRY_CANDLE_TOLERANCE_PCT = float(config.get("ENTRY_CANDLE_TOLERANCE_PCT", 0.1))
ENTRY_ATR_AVG_PERIOD = int(config.get("ENTRY_ATR_AVG_PERIOD", 5))
ENTRY_VOL_AVG_PERIOD = int(config.get("ENTRY_VOL_AVG_PERIOD", 5))
MIN_WATCHLIST_DURATION_MIN = int(config["MIN_WATCHLIST_DURATION_MIN"])
MAX_PRICE_DEVIATION_PERCENT = float(config["MAX_PRICE_DEVIATION_PERCENT"])
MEXC_SYMBOL = symbol.MEXC_SYMBOL
VOLUME_FACTOR = symbol.VOLUME_FACTOR
SYMBOL_FACTOR_MAP = dict(zip(MEXC_SYMBOL, VOLUME_FACTOR))

# --- Biến toàn cục ---
open_orders: Dict[str, dict] = {}
excluded_symbols: Dict[str, datetime] = {}
EXCLUSION_DURATION_MIN = 5
current_balance = BASE_BALANCE
last_summary_time = datetime.now(timezone.utc) - timedelta(minutes=ORDER_SUMMARY_INTERVAL_MIN + 1)

# --- Lớp TradingBot chính ---
class TradingBot:
    def __init__(self):
        self.watch_list: Dict[str, dict] = {}
        self.order_warnings_sent: Dict[str, Dict[int, bool]] = {}
        self.kline_cache: Dict[Tuple[str, str], Tuple[pd.DataFrame, datetime]] = {}
        self.price_cache: Dict[str, Tuple[float, float]] = {}
        self.max_cache_size = 1000
        self.cache_ttl = 60
        
        # Khởi tạo DiscordHandler
        self.discord = discord_handler.DiscordHandler(DISCORD_TOKEN, DISCORD_ADMIN_ID, DISCORD_GUILD_ID, DISCORD_CHANNEL)
        self.setup_discord_commands()
        self.discord.start_bot()

    def setup_discord_commands(self):
        """Thiết lập các lệnh Discord"""
        self.discord.register_trading_commands(
            status_callback=self.get_bot_status_info,
            balance_callback=self.get_balance_info,
            orders_callback=self.get_open_orders_info,
            watchlist_callback=self.get_watchlist_info
        )

    def send_discord_message(self, message: str) -> None:
        """Gửi tin nhắn qua Discord sử dụng handler"""
        self.discord.send_message_sync(message)

    def get_bot_status_info(self) -> str:
        """Tạo thông tin trạng thái bot"""
        now_utc = datetime.now(timezone.utc)
        total_equity = self.calculate_total_equity()
        
        status_msg = (
            f"**📊 #BOT_STATUS**\n"
            f"⏰ Thời gian: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
            f"🏦 Số dư: {current_balance:.2f} USDT\n"
            f"💰 Unrealized PNL: {(total_equity - current_balance):.2f} USDT\n"
            f"📈 Total Equity: {total_equity:.2f} USDT\n"
            f"⚙️ Cấu hình: RR={RR_RATIO}, ATR SL={ATR_SL_MULTIPLIER}x({ATR_TIMEFRAME})\n"
            f"📈 Lệnh đang mở: {len(open_orders)}\n"
            f"👀 Watchlist: {len(self.watch_list)} cặp\n"
            f"❌ Excluded: {len(excluded_symbols)} cặp"
        )
        return status_msg

    def get_balance_info(self) -> str:
        """Tạo thông tin chi tiết về số dư và equity"""
        total_equity = self.calculate_total_equity()
        unrealized_pnl = total_equity - current_balance
        
        balance_msg = (
            f"**💰 #BALANCE_INFO**\n"
            f"🏦 Số dư hiện tại: **{current_balance:.2f} USDT**\n"
            f"💵 Số dư ban đầu: {BASE_BALANCE:.2f} USDT\n"
            f"📊 P&L: {(current_balance - BASE_BALANCE):.2f} USDT ({((current_balance - BASE_BALANCE) / BASE_BALANCE * 100):.2f}%)\n"
            f"💰 Unrealized PNL: {unrealized_pnl:.2f} USDT\n"
            f"📈 Total Equity: {total_equity:.2f} USDT\n"
            f"⚙️ Đòn bẩy: {BASE_LEVERAGE}x\n"
            f"💲 Kích thước lệnh: {BASE_USDT:.2f} USDT"
        )
        return balance_msg

    def get_open_orders_info(self) -> str:
        """Tạo thông tin về các lệnh đang mở"""
        now_utc = datetime.now(timezone.utc)
        unrealized_pnl_total = 0.0
        open_orders_summary = []

        for sym, order in open_orders.items():
            current_price = self.get_current_price(sym)
            if current_price is None:
                continue
                
            entry = order["entry"]
            volume = order["volume"]
            factor = SYMBOL_FACTOR_MAP.get(sym, 1.0)
            side = order["side"]
            precision = order.get('precision', 2)
            
            duration = now_utc - order['open_time']
            total_seconds = int(duration.total_seconds())
            hours, rem = divmod(total_seconds, 3600)
            minutes, seconds = divmod(rem, 60)
            duration_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

            if side == "LONG":
                pnl = (current_price - entry) * volume * factor
            else:
                pnl = (entry - current_price) * volume * factor
            unrealized_pnl_total += pnl

            pnl_percent = (pnl / (entry * volume * factor / BASE_LEVERAGE)) * 100
            
            symbol_base = sym.split('_')[0]
            open_orders_summary.append(
                f"🔹 #{symbol_base} ({side})\n"
                f"   Entry: {entry:.{precision}f} | Current: {current_price:.{precision}f}\n"
                f"   PNL: {pnl:.2f} USDT ({pnl_percent:.2f}%)\n"
                f"   TP: {order['tp']:.{precision}f} | SL: {order['sl']:.{precision}f}\n"
                f"   Thời gian: {duration_str}"
            )

        if not open_orders:
            orders_msg = (
                f"**📊 #ORDERS_INFO**\n"
                f"⏰ Thời gian: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
                f"📈 Lệnh đang mở: 0\n\n"
                f"Hiện không có lệnh nào đang mở."
            )
        else:
            orders_msg = (
                f"**📊 #ORDERS_INFO**\n"
                f"⏰ Thời gian: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
                f"📈 Lệnh đang mở: {len(open_orders)}\n"
                f"💰 Tổng Unrealized PNL: {unrealized_pnl_total:.2f} USDT\n\n"
            ) + "\n".join(open_orders_summary)
            
        return orders_msg

    def get_watchlist_info(self) -> str:
        """Tạo thông tin về watchlist"""
        now_utc = datetime.now(timezone.utc)
        watchlist_summary = []

        if not self.watch_list:
            return (
                f"**👀 #WATCHLIST_INFO**\n"
                f"⏰ Thời gian: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
                f"📈 Cặp theo dõi: 0\n\n"
                f"Hiện không có cặp nào trong watchlist."
            )

        for sym, data in self.watch_list.items():
            side = data['side']
            added_time = data['added_time']
            time_in_watchlist = (now_utc - added_time).total_seconds() / 60
            price_at_add = data['price_at_add']
            rsi_at_add = data.get('m1_rsi_at_add', 'N/A')
            
            current_price = None
            price_change = "N/A"
            try:
                df_m1 = self.get_kline_data(sym, "Min1", limit=5)
                if df_m1 is not None and not df_m1.empty:
                    current_price = df_m1['close'].iloc[-1]
                    price_change = f"{((current_price - price_at_add) / price_at_add * 100):.2f}%"
            except Exception:
                pass
                
            symbol_base = sym.split('_')[0]
            watchlist_summary.append(
                f"👁️ #{symbol_base} ({side})\n"
                f"   Thêm: {time_in_watchlist:.1f} phút trước\n"
                f"   RSI (M1): {rsi_at_add if isinstance(rsi_at_add, float) else 'N/A'}\n"
                f"   Giá thêm: {price_at_add:.6f}\n"
                f"   Thay đổi: {price_change}"
            )

        watchlist_msg = (
            f"**👀 #WATCHLIST_INFO**\n"
            f"⏰ Thời gian: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
            f"📈 Cặp theo dõi: {len(self.watch_list)}\n\n"
        ) + "\n\n".join(watchlist_summary)
        
        return watchlist_msg

    def get_current_price(self, sym: str) -> Optional[float]:
        now = time.time()
        if sym in self.price_cache:
            price, timestamp = self.price_cache[sym]
            if now - timestamp < self.cache_ttl and sym in open_orders:
                return price
            elif now - timestamp >= self.cache_ttl or sym not in open_orders:
                del self.price_cache[sym]

        df_m1 = self.get_kline_data(sym, "Min1", limit=10)
        if df_m1 is None or df_m1.empty:
            error_logger.warning(f"Không lấy được dữ liệu M1 cho {sym}")
            return None
        current_price = df_m1['close'].iloc[-1]

        if len(self.price_cache) >= self.max_cache_size:
            self.cleanup_cache()
        self.price_cache[sym] = (current_price, now)
        return current_price

    def cleanup_cache(self) -> None:
        now = time.time()
        symbols_to_remove = []
        for sym, (price, timestamp) in self.price_cache.items():
            if sym not in open_orders or (now - timestamp >= self.cache_ttl):
                symbols_to_remove.append(sym)

        for sym in symbols_to_remove:
            del self.price_cache[sym]

        if len(self.price_cache) >= self.max_cache_size:
            sorted_cache = sorted(self.price_cache.items(), key=lambda x: x[1][1])
            excess_count = len(self.price_cache) - self.max_cache_size + 1
            for sym, _ in sorted_cache[:excess_count]:
                del self.price_cache[sym]
                logic_logger.info(f"Đã xóa {sym} khỏi cache do vượt giới hạn kích thước")

    def calculate_total_equity(self) -> float:
        unrealized_pnl_total = 0.0
        for sym, order in open_orders.items():
            current_price = self.get_current_price(sym)
            if current_price is None:
                continue
            entry = order["entry"]
            volume = order["volume"]
            factor = SYMBOL_FACTOR_MAP.get(sym, 1.0)
            side = order["side"]
            if side == "LONG":
                pnl = (current_price - entry) * volume * factor
            else:
                pnl = (entry - current_price) * volume * factor
            unrealized_pnl_total += pnl
        return current_balance + unrealized_pnl_total

    def get_kline_data(self, sym: str, interval: str, limit: Optional[int] = None, start: Optional[int] = None, end: Optional[int] = None) -> Optional[pd.DataFrame]:
        now_utc = datetime.now(timezone.utc)
        if sym in excluded_symbols and now_utc < excluded_symbols[sym]:
            return None
        cache_key = (sym, interval)
        if cache_key in self.kline_cache:
            df_cached, cache_time = self.kline_cache[cache_key]
            if (now_utc - cache_time).total_seconds() < 60:
                return df_cached.copy()

        try:
            params = {'interval': interval}
            if limit:
                now_ts = int(now_utc.timestamp())
                interval_seconds = {'Min1': 60, 'Min5': 300, 'Min15': 900, 'Min30': 1800, 'Min60': 3600,
                                    'Hour4': 14400, 'Hour8': 28800, 'Day1': 86400}.get(interval, 60)
                params['start'] = now_ts - (limit + 10) * interval_seconds
                params['end'] = now_ts
            if start:
                params['start'] = start
            if end:
                params['end'] = end
            response = get_kline(sym, **params)
            if not response.get("success"):
                error_logger.error(f"Lỗi API MEXC cho {sym}: {response.get('msg', 'No message')}")
                excluded_symbols[sym] = now_utc + timedelta(minutes=EXCLUSION_DURATION_MIN)
                return None
            df = pd.DataFrame(response["data"])
            df.rename(columns={'time': 'timestamp', 'vol': 'volume', 'amount': 'turnover'}, inplace=True)
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
            df.set_index('timestamp', inplace=True)
            df[['open', 'high', 'low', 'close', 'volume', 'turnover']] = df[['open', 'high', 'low', 'close', 'volume', 'turnover']].apply(pd.to_numeric, errors='coerce')
            df.dropna(subset=['open', 'high', 'low', 'close'], inplace=True)
            if df.empty:
                error_logger.warning(f"DataFrame rỗng cho {sym} ({interval})")
                return None
            self.kline_cache[cache_key] = (df, now_utc)
            return df
        except Exception as e:
            error_logger.error(f"Lỗi lấy kline cho {sym}: {e}")
            excluded_symbols[sym] = now_utc + timedelta(minutes=EXCLUSION_DURATION_MIN)
            return None

    def get_price_precision(self, price: float) -> int:
        from decimal import Decimal
        try:
            price_decimal = Decimal(str(price))
            exponent = price_decimal.as_tuple().exponent
            return min(abs(exponent), 8) if isinstance(exponent, int) and exponent < 0 else 0
        except Exception:
            return 2

    def check_m1_conditions(self, sym: str) -> Tuple[bool, Optional[float], Optional[float], Optional[str]]:
        df_m1 = self.get_kline_data(sym, "Min1", limit=max(50, M1_VOLUME_AVG_PERIOD + 10))
        if df_m1 is None or df_m1.empty:
            return False, None, None, None

        rsi_14 = strategy.calculate_ha_rsi(df_m1.copy(), rsi_period=14, smooth_period=1)
        if rsi_14 is None:
            error_logger.warning(f"{sym} M1: Tính toán HA-RSI thất bại")
            return False, None, None, None

        if 'volume' not in df_m1.columns or len(df_m1['volume']) < M1_VOLUME_AVG_PERIOD + 1:
            error_logger.warning(f"{sym} M1: Dữ liệu volume không đủ (cần > {M1_VOLUME_AVG_PERIOD})")
            return False, rsi_14, None, None

        current_volume = df_m1['volume'].iloc[-1]
        avg_volume = df_m1['volume'].iloc[-(M1_VOLUME_AVG_PERIOD+1):-1].mean()

        if pd.isna(avg_volume) or avg_volume == 0:
            error_logger.warning(f"{sym} M1: Không tính được trung bình volume")
            volume_condition_met = False
        else:
            volume_condition_met = current_volume > avg_volume

        side = None
        if rsi_14 < -RSI_THRESHOLD:
            side = "LONG"
        elif rsi_14 > RSI_THRESHOLD:
            side = "SHORT"

        condition_met = side is not None and volume_condition_met
        current_price = df_m1['close'].iloc[-1] if not df_m1.empty else None
        return condition_met, rsi_14, current_price, side

    def update_watch_list(self, sym: str) -> None:
        if sym in open_orders or sym in excluded_symbols:
            return

        now_utc = datetime.now(timezone.utc)
        try:
            m1_condition_met, rsi_14, current_price, side = self.check_m1_conditions(sym)
            if m1_condition_met and current_price is not None and side is not None:
                if sym not in self.watch_list:
                    precision = self.get_price_precision(current_price)
                    self.watch_list[sym] = {
                        'side': side,
                        'added_time': now_utc,
                        'm1_rsi_at_add': rsi_14,
                        'price_at_add': current_price,
                        'precision': precision,
                        'last_check_time': now_utc
                    }
                    logic_logger.info(f"Thêm {sym} vào watchlist ({side}) - RSI: {rsi_14:.2f}")
                else:
                    self.watch_list[sym]['last_check_time'] = now_utc
        except Exception as e:
            error_logger.error(f"Lỗi cập nhật watchlist cho {sym}: {e}")
            excluded_symbols[sym] = now_utc + timedelta(minutes=EXCLUSION_DURATION_MIN)

    def cleanup_watch_list(self) -> None:
        now_utc = datetime.now(timezone.utc)
        symbols_to_remove = []
        for sym, data in self.watch_list.items():
            time_in_watchlist = now_utc - data['added_time']
            if time_in_watchlist.total_seconds() > WATCHLIST_RETENTION_MINUTES * 60:
                symbols_to_remove.append(sym)
                logic_logger.info(f"Xóa {sym} khỏi watchlist do hết thời gian")

        for sym in symbols_to_remove:
            if sym in self.watch_list:
                del self.watch_list[sym]

    def check_entry_conditions(self, sym: str, side: str) -> Tuple[bool, Optional[str], Optional[float]]:
        logic_logger.debug(f"Kiểm tra điều kiện vào lệnh M5 cho {sym} ({side})")
        m5_met, m5_reason, m5_entry_price = self._check_timeframe_conditions(sym, side, "Min5")
        if m5_met:
            return True, f"M5: {m5_reason}", m5_entry_price

        logic_logger.debug(f"Kiểm tra điều kiện vào lệnh M15 cho {sym} ({side})")
        m15_met, m15_reason, m15_entry_price = self._check_timeframe_conditions(sym, side, "Min15")
        if m15_met:
            return True, f"M15: {m15_reason}", m15_entry_price

        return False, "Điều kiện M5/M15 không đạt", None

    def _check_timeframe_conditions(self, sym: str, side: str, interval: str) -> Tuple[bool, Optional[str], Optional[float]]:
        df = self.get_kline_data(sym, interval, limit=max(50, ATR_PERIOD + ENTRY_ATR_AVG_PERIOD + 2))
        min_required_candles = max(3, ATR_PERIOD + 1, ENTRY_ATR_AVG_PERIOD + 1, ENTRY_VOL_AVG_PERIOD + 1)
        if df is None or len(df) < min_required_candles:
            return False, f"{interval} dữ liệu không đủ (cần {min_required_candles})", None

        interval_seconds = {'Min5': 300, 'Min15': 900}.get(interval, 60)
        last_candle_time = df.index[-1].to_pydatetime()
        if (datetime.now(timezone.utc) - last_candle_time).total_seconds() > 2.5 * interval_seconds:
            return False, f"{interval} dữ liệu cũ", None

        current_price = df['close'].iloc[-1]
        precision = self.get_price_precision(current_price)

        candle_pattern_ok = False
        try:
            low_n1, low_n2, low_n3 = df['low'].iloc[-1], df['low'].iloc[-2], df['low'].iloc[-3]
            high_n1, high_n2, high_n3 = df['high'].iloc[-1], df['high'].iloc[-2], df['high'].iloc[-3]
            tolerance = current_price * (ENTRY_CANDLE_TOLERANCE_PCT / 100.0)
            if side == "LONG":
                c1 = low_n1 >= low_n2 - tolerance
                c2 = low_n2 >= low_n3 - tolerance
                if c1 or c2:
                    candle_pattern_ok = True
            elif side == "SHORT":
                c1 = high_n1 <= high_n2 + tolerance
                c2 = high_n2 <= high_n3 + tolerance
                if c1 or c2:
                    candle_pattern_ok = True
        except IndexError:
            candle_pattern_ok = False
        if not candle_pattern_ok:
            return False, f"{interval} mô hình nến không đạt", None

        atr = strategy.calculate_atr(df.copy(), period=ATR_PERIOD)
        if atr is None:
            return False, f"{interval} tính toán ATR thất bại", None

        volatility_decreased = False
        try:
            if len(df) >= ATR_PERIOD + ENTRY_ATR_AVG_PERIOD + 1:
                atr_series = ATR(df['high'], df['low'], df['close'], timeperiod=ATR_PERIOD)
                avg_atr_prev_N = atr_series.iloc[-(ENTRY_ATR_AVG_PERIOD+1):-1].mean()
                if not pd.isna(avg_atr_prev_N) and atr < avg_atr_prev_N:
                    volatility_decreased = True
            elif len(df) >= ATR_PERIOD + 2:
                atr_series = ATR(df['high'], df['low'], df['close'], timeperiod=ATR_PERIOD)
                atr_prev = atr_series.iloc[-2]
                if not pd.isna(atr_prev) and atr < atr_prev:
                    volatility_decreased = True
        except Exception:
            return False, f"{interval} biến động không giảm (ATR)", None

        volume_confirmed = False
        try:
            if 'volume' not in df.columns:
                return False, f"{interval} thiếu dữ liệu volume", None
            if len(df['volume']) >= ENTRY_VOL_AVG_PERIOD + 1:
                vol_n1 = df['volume'].iloc[-1]
                avg_vol_prev_N = df['volume'].iloc[-(ENTRY_VOL_AVG_PERIOD+1):-1].mean()
                if not pd.isna(avg_vol_prev_N) and vol_n1 < avg_vol_prev_N:
                    volume_confirmed = True
            elif len(df['volume']) >= 2:
                vol_n1, vol_n2 = df['volume'].iloc[-1], df['volume'].iloc[-2]
                if vol_n1 < vol_n2:
                    volume_confirmed = True
        except Exception:
            return False, f"{interval} xác nhận volume thất bại", None

        entry_offset_factor = 0.0002
        entry_price = round(
            current_price * (1 - entry_offset_factor) if side == "LONG" else current_price * (1 + entry_offset_factor),
            precision
        )
        return True, f"{interval} điều kiện đạt", entry_price

    def place_trade_from_watchlist(self, sym: str) -> None:
        if sym not in self.watch_list:
            return
        if sym in open_orders:
            if sym in self.watch_list:
                del self.watch_list[sym]
            return

        data = self.watch_list[sym]
        side = data['side']
        added_time = data['added_time']
        price_at_add = data['price_at_add']
        now_utc = datetime.now(timezone.utc)
        time_in_watchlist = (now_utc - added_time).total_seconds() / 60

        if time_in_watchlist < MIN_WATCHLIST_DURATION_MIN:
            return

        df_m1 = self.get_kline_data(sym, "Min1", limit=10)
        if df_m1 is None or df_m1.empty:
            return
        current_price = df_m1['close'].iloc[-1]
        price_change_pct = abs((current_price - price_at_add) / price_at_add * 100)
        if price_change_pct > MAX_PRICE_DEVIATION_PERCENT:
            del self.watch_list[sym]
            return

        try:
            entry_conditions_met, reason, entry_price = self.check_entry_conditions(sym, side)
            if entry_conditions_met and entry_price is not None:
                precision = data.get('precision', self.get_price_precision(entry_price))
                factor = SYMBOL_FACTOR_MAP.get(sym)
                if factor is None:
                    error_logger.error(f"Không tìm thấy volume factor cho {sym}")
                    return
                volume = int((BASE_USDT * BASE_LEVERAGE) / (entry_price * factor))
                if volume <= 0:
                    error_logger.warning(f"{sym}: Volume tính được không hợp lệ ({volume})")
                    return

                sl, tp = self.calculate_sl_tp(sym, side, entry_price, precision)
                if sl is None or tp is None:
                    return

                tp_pnl = abs(tp - entry_price) * volume * factor
                sl_pnl = -abs(sl - entry_price) * volume * factor

                self.execute_order(sym, side, entry_price, tp, sl, volume, tp_pnl, sl_pnl)
                if sym in self.watch_list:
                    del self.watch_list[sym]
        except Exception as e:
            error_logger.error(f"Lỗi trong place_trade_from_watchlist cho {sym}: {e}")
            excluded_symbols[sym] = now_utc + timedelta(minutes=EXCLUSION_DURATION_MIN)

    def calculate_sl_tp(self, sym: str, side: str, entry_price: float, precision: int) -> Tuple[Optional[float], Optional[float]]:
        df_sl = self.get_kline_data(sym, ATR_TIMEFRAME, limit=max(50, ATR_PERIOD + 5))
        if df_sl is None or df_sl.empty:
            return None, None

        atr = strategy.calculate_atr(df_sl.copy(), period=ATR_PERIOD)
        if atr is None or atr <= 0:
            return None, None

        sl_distance_atr = atr * ATR_SL_MULTIPLIER
        sl_distance_min_pct = entry_price * MIN_SL_PERCENTAGE
        sl_distance = max(sl_distance_atr, sl_distance_min_pct)

        if side == "LONG":
            sl_price = entry_price - sl_distance
            tp_price = entry_price + sl_distance * RR_RATIO
        else:
            sl_price = entry_price + sl_distance
            tp_price = entry_price - sl_distance * RR_RATIO

        sl_price_rounded = round(sl_price, precision)
        tp_price_rounded = round(tp_price, precision)

        buffer = 1 / (10**(precision + 1))
        if side == "LONG":
            if sl_price_rounded >= entry_price - buffer:
                sl_price_rounded = round(entry_price - sl_distance_min_pct, precision)
            if tp_price_rounded <= entry_price + buffer:
                return None, None
        elif side == "SHORT":
            if sl_price_rounded <= entry_price + buffer:
                sl_price_rounded = round(entry_price + sl_distance_min_pct, precision)
            if tp_price_rounded >= entry_price - buffer:
                return None, None

        return sl_price_rounded, tp_price_rounded

    def execute_order(self, sym: str, side: str, entry: float, tp: float, sl: float, volume: int, tp_pnl: float, sl_pnl: float) -> None:
        global current_balance
        precision = self.get_price_precision(entry)
        now_utc = datetime.now(timezone.utc)

        # Chuẩn bị tham số cho place_order
        is_long = side == "LONG"
        is_market = False  # Giả sử dùng limit order
        symbol_base = sym  # Ví dụ: BTC_USDT

        # Gọi place_order từ basic_call.py
        success, result = place_order(
            symbol=symbol_base,
            price=entry,
            tp=tp,
            sl=sl,
            volume=volume,
            leverage=BASE_LEVERAGE,
            is_market=is_market,
            is_long=is_long
        )

        # Lấy order ID từ kết quả
        order_id = result.get("data", {}).get("orderId") if success else None

        # Chuẩn bị tin nhắn Telegram
        total_equity = self.calculate_total_equity()
        symbol_display = sym.split('_')[0]
        order_msg_base = (
            f"**{'🟢' if side == 'LONG' else '🔴'} #NEW_ORDER**\n"
            f" Symbol: #{symbol_display}\n"
            f" Side: **{side}**\n"
            f" Entry: {entry:.{precision}f}\n"
            f" TP: {tp:.{precision}f} (*~+{tp_pnl:.2f} USDT*)\n"
            f" SL: {sl:.{precision}f} (*~{sl_pnl:.2f} USDT*)\n"
            f"💲 Value: {BASE_USDT:.2f} USDT (x{BASE_LEVERAGE} Lev)\n"
            f"⏰ Time: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
            f"🏦 Balance: {current_balance:.2f} USDT\n"
            f"💰 Unrealized PNL: {(total_equity - current_balance):.2f} USDT\n"
            f"📈 Total Equity: {total_equity:.2f} USDT"
        )

        if success and order_id:
            # Lưu thông tin lệnh
            open_orders[sym] = {
                "symbol": sym,
                "side": side,
                "entry": entry,
                "tp": tp,
                "sl": sl,
                "volume": volume,
                "order_id": order_id,
                "open_time": now_utc,
                "precision": precision,
                "tp_pnl_est": tp_pnl,
                "sl_pnl_est": sl_pnl
            }
            self.order_warnings_sent[order_id] = {ORDER_WARN_DURATION_H1: False, ORDER_WARN_DURATION_H2: False}
            self.send_discord_message(order_msg_base + f"\n**✅ Order Placed Successfully! Order ID: {order_id}**")
            trade_logger.info(f"Đặt lệnh thành công {order_id} cho {sym} ({side})")
        else:
            error_msg = result.get("msg", "Lỗi không xác định") if not success else "Không tìm thấy Order ID"
            self.send_discord_message(order_msg_base + f"\n**❌ Order Placement Failed! Error: {error_msg}**")
            trade_logger.error(f"Không thể đặt lệnh cho {sym} ({side}): {error_msg}")
            excluded_symbols[sym] = now_utc + timedelta(minutes=EXCLUSION_DURATION_MIN)

    def manage_open_orders(self) -> None:
        if not open_orders:
            return
        now_utc = datetime.now(timezone.utc)

        for sym in list(open_orders.keys()):
            if sym not in open_orders:
                continue

            order = open_orders[sym]
            order_id = order["order_id"]
            open_time = order["open_time"]
            duration = now_utc - open_time

            try:
                df_m1 = self.get_kline_data(sym, "Min1", limit=10)
                if df_m1 is None or df_m1.empty:
                    error_logger.warning(f"Không lấy được dữ liệu M1 để quản lý lệnh {sym} ({order_id})")
                    continue

                last_candle_time_m1 = df_m1.index[-1].to_pydatetime()
                if (now_utc - last_candle_time_m1).total_seconds() > 150:
                    error_logger.warning(f"{sym} dữ liệu M1 có vẻ cũ (nến cuối: {last_candle_time_m1})")
                    continue

                recent_high = df_m1['high'].iloc[-3:].max()
                recent_low = df_m1['low'].iloc[-3:].min()
                current_price = df_m1['close'].iloc[-1]
                precision = order.get('precision', self.get_price_precision(current_price))

                closed = self.check_order_closure(sym, order, current_price, precision, recent_high, recent_low)
                if closed:
                    if order_id in self.order_warnings_sent:
                        del self.order_warnings_sent[order_id]
                    continue

                self.check_order_duration_warnings(order_id, sym, duration)

            except Exception as e:
                error_logger.error(f"Lỗi khi quản lý lệnh mở {sym} ({order_id}): {e}", exc_info=True)
                excluded_symbols[sym] = now_utc + timedelta(minutes=EXCLUSION_DURATION_MIN)

    def check_order_closure(self, sym: str, order: dict, current_price: float, precision: int, recent_high: float, recent_low: float) -> bool:
        global current_balance
        if sym not in open_orders or order['order_id'] != open_orders[sym]['order_id']:
            error_logger.warning(f"Lệnh {order['order_id']} cho {sym} có vẻ đã đóng hoặc thay đổi")
            return False

        entry, tp, sl = order["entry"], order["tp"], order["sl"]
        volume, side = order["volume"], order["side"]
        order_id = order["order_id"]
        open_time = order["open_time"]
        factor = SYMBOL_FACTOR_MAP.get(sym, 1.0)
        now_utc = datetime.now(timezone.utc)

        hit_tp, hit_sl = False, False
        close_price = current_price
        if side == "LONG":
            if recent_high >= tp:
                hit_tp = True
                close_price = tp
            elif recent_low <= sl:
                hit_sl = True
                close_price = sl
        elif side == "SHORT":
            if recent_low <= tp:
                hit_tp = True
                close_price = tp
            elif recent_high >= sl:
                hit_sl = True
                close_price = sl

        if not hit_tp and not hit_sl:
            return False

        pnl = (close_price - entry) * volume * factor if side == "LONG" else (entry - close_price) * volume * factor
        status = f"✅ TP Đạt tại {tp:.{precision}f}" if hit_tp else f"❌ SL Đạt tại {sl:.{precision}f}"
        previous_balance = current_balance
        current_balance += pnl

        symbol_base = sym.split('_')[0]
        duration = now_utc - open_time
        total_seconds = int(duration.total_seconds())
        hours, rem = divmod(total_seconds, 3600)
        minutes, seconds = divmod(rem, 60)
        duration_str = f"{hours}h {minutes}m {seconds}s"
        total_equity = self.calculate_total_equity()

        close_msg = (
            f"**{'✅' if hit_tp else '❌'} #CLOSED_ORDER**\n"
            f" Symbol: #{symbol_base}\n"
            f" Side: {side}\n"
            f" Entry: {entry:.{precision}f}\n"
            f" Status: {status} (Trigger Price: {close_price:.{precision}f})\n"
            f" PNL: {pnl:+.2f} USDT\n"
            f"⏱️ Duration: {duration_str}\n"
            f"💲 Value: {BASE_USDT:.2f} USDT\n"
            f"🏦 Balance: {previous_balance:.2f} -> **{current_balance:.2f} USDT**\n"
            f"💰 Unrealized PNL: {(total_equity - current_balance):.2f} USDT\n"
            f"📈 Total Equity: {total_equity:.2f} USDT"
        )
        self.send_discord_message(close_msg)
        trade_logger.info(f"Đã đóng lệnh {order_id} cho {sym} ({side}) với trạng thái {status}, PNL: {pnl:.2f} USDT")

        if sym in open_orders and open_orders[sym]['order_id'] == order_id:
            del open_orders[sym]
            self.cleanup_cache()

        if current_balance < 0:
            self.send_discord_message(f"**⚠️ CẢNH BÁO NGHIÊM TRỌNG**\nSố dư tài khoản âm: {current_balance:.2f} USDT")
        return True

    def check_order_duration_warnings(self, order_id: str, sym: str, duration: timedelta) -> None:
        if sym not in open_orders or open_orders[sym]['order_id'] != order_id:
            if order_id in self.order_warnings_sent:
                del self.order_warnings_sent[order_id]
            return

        order = open_orders[sym]
        precision = order.get('precision', 2)
        duration_hours = duration.total_seconds() / 3600

        if order_id not in self.order_warnings_sent:
            self.order_warnings_sent[order_id] = {ORDER_WARN_DURATION_H1: False, ORDER_WARN_DURATION_H2: False}

        symbol_base = sym.split('_')[0]

        h1_warn_key = ORDER_WARN_DURATION_H1
        if duration_hours >= h1_warn_key and not self.order_warnings_sent[order_id].get(h1_warn_key, False):
            warn_msg = (
                f"**⚠️ #ORDER_DURATION_WARNING**\n"
                f"💱 Symbol: #{symbol_base}\n"
                f"⏱️ Thời gian mở: > {h1_warn_key} giờ ({duration_hours:.1f}h)\n"
                f"📊 Side: {order['side']} | Entry: {order['entry']:.{precision}f}\n"
                f"✅ TP: {order['tp']:.{precision}f} | ❌ SL: {order['sl']:.{precision}f}"
            )
            self.send_discord_message(warn_msg)
            trade_logger.warning(f"Đã gửi cảnh báo thời gian {h1_warn_key}h cho lệnh {order_id} ({sym})")
            self.order_warnings_sent[order_id][h1_warn_key] = True

        h2_warn_key = ORDER_WARN_DURATION_H2
        if duration_hours >= h2_warn_key and not self.order_warnings_sent[order_id].get(h2_warn_key, False):
            warn_msg = (
                f"**🚨 #ORDER_DURATION_ALERT**\n"
                f"💱 Symbol: #{symbol_base}\n"
                f"⏱️ Thời gian mở: > {h2_warn_key} giờ ({duration_hours:.1f}h)\n"
                f"📊 Side: {order['side']} | Entry: {order['entry']:.{precision}f}\n"
                f"✅ TP: {order['tp']:.{precision}f} | ❌ SL: {order['sl']:.{precision}f}"
            )
            self.send_discord_message(warn_msg)
            trade_logger.warning(f"Đã gửi cảnh báo thời gian {h2_warn_key}h cho lệnh {order_id} ({sym})")
            self.order_warnings_sent[order_id][h2_warn_key] = True

    def send_open_orders_summary(self) -> None:
        global last_summary_time
        now_utc = datetime.now(timezone.utc)
        if (now_utc - last_summary_time).total_seconds() < ORDER_SUMMARY_INTERVAL_MIN * 60:
            return

        unrealized_pnl_total = 0.0
        open_orders_summary = []

        for sym, order in open_orders.items():
            current_price = self.get_current_price(sym)
            if current_price is None:
                error_logger.warning(f"Không lấy được giá hiện tại cho {sym} trong tóm tắt")
                continue
            entry = order["entry"]
            volume = order["volume"]
            factor = SYMBOL_FACTOR_MAP.get(sym, 1.0)
            side = order["side"]
            precision = order.get('precision', 2)
            duration = now_utc - order['open_time']
            total_seconds = int(duration.total_seconds())
            hours, rem = divmod(total_seconds, 3600)
            minutes, seconds = divmod(rem, 60)
            duration_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

            if side == "LONG":
                pnl = (current_price - entry) * volume * factor
            else:
                pnl = (entry - current_price) * volume * factor
            unrealized_pnl_total += pnl

            symbol_base = sym.split('_')[0]
            open_orders_summary.append(
                f"🔹 #{symbol_base} ({side})\n"
                f"   Entry: {entry:.{precision}f} | Current: {current_price:.{precision}f}\n"
                f"   PNL: {pnl:.2f} USDT | Dur: {duration_str}\n"
                f"   TP: {order['tp']:.{precision}f} | SL: {order['sl']:.{precision}f}\n"
            )

        total_equity = current_balance + unrealized_pnl_total
        summary_msg = (
            f"**📊 #ORDER_SUMMARY**\n"
            f"⏰ Time: {now_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
            f"🏦 Balance: {current_balance:.2f} USDT\n"
            f"💰 Unrealized PNL: {unrealized_pnl_total:.2f} USDT\n"
            f"📈 Total Equity: {total_equity:.2f} USDT\n"
        )

        if not open_orders:
            summary_msg += f"📈 Open Orders: 0\n\nKhông có lệnh mở."
        else:
            summary_msg += f"📈 Open Orders: {len(open_orders)}\n\n" + "\n".join(open_orders_summary)

        self.send_discord_message(summary_msg)
        last_summary_time = now_utc

    def cleanup(self):
        """Dọn dẹp tài nguyên khi dừng bot"""
        if hasattr(self, 'discord'):
            self.discord.stop_bot_updates()

    def run(self) -> None:
        trade_logger.info("Khởi động Trading Bot...")
        self.send_discord_message(
            f"**🤖 Trading Bot Khởi tạo**\n"
            f"⏰ Time: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S %Z')}\n"
            f"🏦 Số dư ban đầu: {BASE_BALANCE:.2f} USDT\n"
            f"⚙️ Config: RR={RR_RATIO}, SL ATR={ATR_SL_MULTIPLIER}x({ATR_TIMEFRAME})"
        )
        try:
            while True:
                try:
                    now_utc = datetime.now(timezone.utc)

                    expired_exclusions = [sym for sym, expiry in excluded_symbols.items() if now_utc > expiry]
                    for sym in expired_exclusions:
                        del excluded_symbols[sym]
                        logic_logger.info(f"Xóa {sym} khỏi danh sách loại trừ")

                    symbols_to_check = [s for s in MEXC_SYMBOL if s not in open_orders and s not in excluded_symbols]
                    if symbols_to_check:
                        for sym in symbols_to_check:
                            self.update_watch_list(sym)
                            time.sleep(max(0.05, min(0.25, 5 / len(MEXC_SYMBOL))))

                    if self.watch_list:
                        watchlist_keys = list(self.watch_list.keys())
                        for sym in watchlist_keys:
                            if sym in self.watch_list and sym not in open_orders and sym not in excluded_symbols:
                                self.place_trade_from_watchlist(sym)
                                time.sleep(random.uniform(0.2, 0.4))

                    if open_orders:
                        self.manage_open_orders()

                    self.cleanup_watch_list()
                    self.send_open_orders_summary()

                    time.sleep(SLEEP_INTERVAL)

                except KeyboardInterrupt:
                    trade_logger.info("Nhận tín hiệu dừng từ bàn phím. Tắt bot...")
                    self.send_discord_message("**🤖 Trading Bot Đang Tắt**")
                    break
                except Exception as e:
                    error_logger.exception(f"Lỗi trong vòng lặp chính: {e}")
                    self.send_discord_message(f"**🆘 LỖI BOT:**\n```{str(e)[:300]}```")
                    time.sleep(SLEEP_INTERVAL * 2)
        finally:
            self.cleanup()

if __name__ == "__main__":
    bot = TradingBot()
    try:
        bot.run()
    except KeyboardInterrupt:
        bot.cleanup()