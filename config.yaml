# Thông tin API và Discord
API_KEY: "mx0vglm1paOJPlT5DG"
SECRET_KEY: "3ce57b71b04146aca28cce733001640b"
SIM_KEY: "WEB7a163c618e2fc632f7d3ae6f6cb22705a1d59c5e5d5db08fd3bbded24f6f9f12"

# Discord Configuration
DISCORD_TOKEN: "MTM3NzU0MTk3MTAwNTkzNTYyNw.G4HoEk.KpHi8Nmv0RDoEHDt3NLsikJ-fNGAmfyFg5B-38"
DISCORD_ADMIN_ID: "1365018104249450540"
DISCORD_GUILD_ID: "1375879723296489562"
DISCORD_CHANNEL: "mexc-auto"

BALANCE_UPDATE_INTERVAL_MIN: 120  # Cập nhật số dư mỗi 15 phút
ORDER_CHECK_TIMEOUT_MIN: 15        # Kiểm tra lệnh chưa khớp sau 5 phút
BASE_USDT: 0.1                    # Số tiền USDT cơ bản cho mỗi lệnh
BASE_LEVERAGE: 200                # Đòn bẩy giao dịch (200x)
BASE_BALANCE: 100.0               # Số dư ban đầu của tài khoản
RR_RATIO: 1.7                     # Tỷ lệ Rủi ro/Lợi nhuận (Risk:Reward)

# Cấu hình thời gian
SLEEP_INTERVAL: 15                # Thời gian nghỉ giữa các vòng lặp chính của bot (giây)
WATCHLIST_RETENTION_MINUTES: 15   # Thời gian tối đa symbol được giữ trong watchlist (phút)
ORDER_WARN_DURATION_H1: 3         # Số giờ mở lệnh trước khi gửi cảnh báo đầu tiên
ORDER_WARN_DURATION_H2: 6         # Số giờ mở lệnh trước khi gửi cảnh báo thứ hai
ORDER_SUMMARY_INTERVAL_MIN: 120   # Khoảng thời gian giữa các lần gửi tóm tắt lệnh mở (phút)

# Cấu hình Stop Loss (SL) dựa trên ATR
ATR_TIMEFRAME: "Min5"             # Khung thời gian tính ATR cho SL (ví dụ: Min5, Min15)
ATR_PERIOD: 14                    # Số nến để tính ATR
ATR_SL_MULTIPLIER: 2              # Hệ số nhân ATR để xác định khoảng cách SL
MIN_SL_PERCENTAGE: 0.007          # SL tối thiểu theo phần trăm giá vào lệnh (0.006 = 0.6%)

# Cấu hình điều kiện vào watchlist
RSI_THRESHOLD: 30                 # Ngưỡng RSI để thêm symbol vào watchlist (LONG nếu dưới -25, SHORT nếu trên 25)
M1_VOLUME_AVG_PERIOD: 14          # Số nến M1 để tính volume trung bình
M1_VOLUME_MULTIPLIER: 1.5         # Hệ số volume hiện tại so với trung bình để vào watchlist
MIN_WATCHLIST_DURATION_MIN: 10    # Thời gian tối thiểu symbol phải ở trong watchlist trước khi vào lệnh (phút, đã đổi tên từ MIN_WATCHLIST_TIME_MIN)

# Cấu hình điều kiện vào lệnh
ENTRY_CANDLE_TOLERANCE_PCT: 0.5   # Dung sai % khi so sánh đỉnh/đáy nến để xác nhận mô hình (0.5 = 0.5%)
ENTRY_ATR_AVG_PERIOD: 5           # Số nến để tính ATR trung bình khi kiểm tra biến động giảm
ENTRY_VOL_AVG_PERIOD: 5           # Số nến để tính volume trung bình khi xác nhận vào lệnh

# Tham số bổ sung (thêm vào để khớp với mã nguồn trước đó)
MAX_PRICE_DEVIATION_PERCENT: 2    # Độ lệch giá tối đa (%) so với giá lúc vào watchlist (thêm để khớp với mã)
