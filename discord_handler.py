import discord
from discord.ext import commands
import logging
import asyncio
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timezone
import threading
import queue

logger = logging.getLogger('discord')

class DiscordHandler:
    def __init__(self, token: str, admin_id: str, guild_id: str, channel_name: str):
        self.token = token
        self.admin_id = admin_id
        self.guild_id = guild_id
        self.channel_name = channel_name
        self.command_handlers = {}
        self.message_queue = queue.Queue()
        self.stop_bot = False
        self.bot_thread = None
        
        intents = discord.Intents.default()
        intents.message_content = True
        self.bot = commands.Bot(command_prefix='/', intents=intents)
        
        self.register_command('start', self._handle_start)
        self.register_command('help', self._handle_help)
        
        self.setup_bot_events()
        logger.info("DiscordHandler đã được khởi tạo")
    
    def register_command(self, command: str, handler: Callable):
        """<PERSON><PERSON><PERSON> ký một command handler"""
        self.command_handlers[command] = handler
        logger.debug(f"Đã đăng ký handler cho l<PERSON><PERSON>: {command}")
    
    def setup_bot_events(self):
        @self.bot.event
        async def on_ready():
            logger.info(f'Discord bot đã đăng nhập: {self.bot.user}')
            
        @self.bot.event
        async def on_message(message):
            if message.author == self.bot.user:
                return
                
            if str(message.author.id) != self.admin_id:
                logger.warning(f"Tin nhắn từ người dùng không được phép: {message.author.id}")
                return
                
            if message.content.startswith('/'):
                command_parts = message.content[1:].split(' ', 1)
                command = command_parts[0].lower()
                args = command_parts[1] if len(command_parts) > 1 else ""
                
                if command in self.command_handlers:
                    try:
                        await self.command_handlers[command](message, args)
                    except Exception as e:
                        logger.error(f"Lỗi khi xử lý lệnh {command}: {e}")
                        await message.channel.send(f"⚠️ Lỗi xử lý lệnh: {str(e)}")
                else:
                    await message.channel.send(f"❓ Lệnh không được hỗ trợ: {command}")
    
    async def send_message(self, message: str, channel_id: Optional[int] = None) -> bool:
        """Gửi tin nhắn đến channel"""
        try:
            if channel_id:
                channel = self.bot.get_channel(channel_id)
            else:
                guild = self.bot.get_guild(int(self.guild_id))
                if guild:
                    channel = discord.utils.get(guild.channels, name=self.channel_name)
                else:
                    logger.error("Không tìm thấy guild")
                    return False
            
            if channel:
                await channel.send(message)
                return True
            else:
                logger.error(f"Không tìm thấy channel: {self.channel_name}")
                return False
        except Exception as e:
            logger.error(f"Lỗi gửi tin nhắn Discord: {e}")
            return False
    
    def send_message_sync(self, message: str) -> bool:
        """Gửi tin nhắn đồng bộ"""
        if self.bot.is_ready():
            try:
                # Lấy loop từ bot thread
                if hasattr(self.bot, 'loop') and self.bot.loop:
                    future = asyncio.run_coroutine_threadsafe(self.send_message(message), self.bot.loop)
                    future.result(timeout=10)  # Chờ tối đa 10 giây
                    return True
                else:
                    logger.warning("Bot loop không khả dụng")
                    return False
            except Exception as e:
                logger.error(f"Lỗi gửi tin nhắn đồng bộ: {e}")
                return False
        else:
            logger.warning("Bot chưa sẵn sàng")
            return False
    
    def start_bot(self):
        """Bắt đầu Discord bot trong thread riêng"""
        if self.bot_thread and self.bot_thread.is_alive():
            logger.warning("Bot thread đã đang chạy")
            return
            
        self.stop_bot = False
        self.bot_thread = threading.Thread(target=self._bot_worker, daemon=True)
        self.bot_thread.start()
        logger.info("Đã bắt đầu Discord bot")
    
    def stop_bot_updates(self):
        """Dừng Discord bot"""
        self.stop_bot = True
        if self.bot_thread:
            asyncio.run_coroutine_threadsafe(self.bot.close(), self.bot.loop)
            self.bot_thread.join(timeout=5)
            logger.info("Đã dừng Discord bot")
    
    def _bot_worker(self):
        """Worker thread để chạy Discord bot"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.bot.start(self.token))
        except Exception as e:
            logger.error(f"Lỗi trong bot worker: {e}")
    
    async def _handle_start(self, message, args: str):
        """Handler cho lệnh /start"""
        user_name = message.author.display_name
        response = (
            f"🤖 **Chào mừng, {user_name}!**\n\n"
            f"Bot giao dịch đã sẵn sàng. Sử dụng /help để xem danh sách lệnh."
        )
        await message.channel.send(response)
    
    async def _handle_help(self, message, args: str):
        """Handler cho lệnh /help"""
        commands = [
            "/status - Xem thông tin trạng thái bot và tài khoản",
            "/balance - Xem số dư và thông tin equity", 
            "/orders - Xem danh sách lệnh đang mở",
            "/watchlist - Xem danh sách cặp đang theo dõi",
            "/help - Hiển thị menu trợ giúp này"
        ]
        response = "**📋 Danh sách lệnh:**\n" + "\n".join(commands)
        await message.channel.send(response)
    
    async def handle_status(self, message, args: str, status_callback):
        """Xử lý lệnh /status"""
        try:
            status_info = status_callback()
            await message.channel.send(status_info)
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh status: {e}")
            await message.channel.send(f"⚠️ Lỗi lấy thông tin trạng thái: {str(e)}")
    
    async def handle_balance(self, message, args: str, balance_callback):
        """Xử lý lệnh /balance"""
        try:
            balance_info = balance_callback()
            await message.channel.send(balance_info)
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh balance: {e}")
            await message.channel.send(f"⚠️ Lỗi lấy thông tin số dư: {str(e)}")
    
    async def handle_orders(self, message, args: str, orders_callback):
        """Xử lý lệnh /orders"""
        try:
            orders_info = orders_callback()
            await message.channel.send(orders_info)
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh orders: {e}")
            await message.channel.send(f"⚠️ Lỗi lấy thông tin lệnh: {str(e)}")
    
    async def handle_watchlist(self, message, args: str, watchlist_callback):
        """Xử lý lệnh /watchlist"""
        try:
            watchlist_info = watchlist_callback()
            await message.channel.send(watchlist_info)
        except Exception as e:
            logger.error(f"Lỗi xử lý lệnh watchlist: {e}")
            await message.channel.send(f"⚠️ Lỗi lấy thông tin watchlist: {str(e)}")
    
    def register_trading_commands(self, status_callback, balance_callback, orders_callback, watchlist_callback):
        """Đăng ký các callback xử lý cho từng lệnh liên quan đến giao dịch"""
        self.register_command('status', 
                              lambda msg, args: self.handle_status(msg, args, status_callback))
        self.register_command('balance', 
                              lambda msg, args: self.handle_balance(msg, args, balance_callback))
        self.register_command('orders', 
                              lambda msg, args: self.handle_orders(msg, args, orders_callback))
        self.register_command('watchlist', 
                              lambda msg, args: self.handle_watchlist(msg, args, watchlist_callback))
